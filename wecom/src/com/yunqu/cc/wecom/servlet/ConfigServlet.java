package com.yunqu.cc.wecom.servlet;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.wecom.base.AppBaseServlet;
import com.yunqu.cc.wecom.base.CommonLogger;
import com.yunqu.cc.wecom.base.Constants;
import com.yunqu.cc.wecom.inf.MessageService;

/**
 * <AUTHOR> ：tby
 * @version 创建时间：2022年5月7日 下午4:02:33
 */
@WebServlet("/servlet/config")
public class ConfigServlet extends AppBaseServlet{

	private static final long serialVersionUID = 1L;
	private Logger logger = CommonLogger.logger;


	/**
	 * sdk初始化
	 * @return
	 */
	public JSONObject actionForSdkInit() {
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject();
		String url = getJSONObject().getString("url");
		request.put("command", "agentJSSDKInit");
		JSONObject params = new JSONObject();
		params.put("url", url);
		request.put("params", params);
		try {
			IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
			result = service.invoke(request);
			if(result!=null) {
				result.put("agentId", Constants.WECOM_AGENT_ID);
			}
			logger.info("sdk初始化网关返回结果：" + result);
		} catch (ServiceException e) {
			logger.error("jssdk初始化失败" + e.getMessage(), e);
			return EasyResult.error();
		}
		return EasyResult.ok(result);
	}


	/**
	 * 获取外部用户详情
	 * @return
	 */
	public JSONObject actionForGetExtCustInfo() {
		EasyCache cache = CacheManager.getMemcache();
		String userId = getJSONObject().getString("userId");
		// 增加日志记录
		logger.info("开始获取外部联系人信息: userId=" + userId);
		if(userId == null) {
			logger.info("获取客户信息失败，原因userId为空");
			return EasyResult.error(500,"获取客户信息失败，原因userId为空");
		}
		String str = cache.get("WECOM_EXTCUST_INFO_" + userId);
		if(StringUtils.isNotBlank(str)) {
			JSONObject extCustInfo = JSONObject.parseObject(str);
			logger.info("缓存获取到客户信息: userId=" + userId + ", extCustInfo=" + extCustInfo );
			return EasyResult.ok(extCustInfo);
		}
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject();
		request.put("command", "getExtCustInfo");
		JSONObject params = new JSONObject();
		params.put("userId", userId);
		request.put("params", params);
		try {
			IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
			result = service.invoke(request);
			logger.info("获取外部联系人信息接口返回: userId=" + userId + ", result=" + result.toJSONString());
			if(result!=null&&result.getJSONObject("external_contact")!=null) {
				JSONObject extCustInfo = result.getJSONObject("external_contact");
				logger.info("获取外部联系人信息接口返回: userId=" + userId + ", extCustInfo=" + extCustInfo.toJSONString());
				cache.put("WECOM_EXTCUST_INFO_" + userId, extCustInfo.toJSONString(),60*60);
				return EasyResult.ok(extCustInfo);
			}
		} catch (Exception e) {
			logger.info("获取用户信息失败: userId=" + userId + ", error: " + e.getMessage());
			return EasyResult.error();
		}
		CommonLogger.logger.info("未获取到有效的客户信息: userId=" + userId);
		return EasyResult.ok("");
	}

	/**
	 * 保存坐席的成员id和外部联系人成员id
	 */
	public void actionForSaveAgentAndCustomerInfo() {
		JSONObject reqData = getJSONObject();
		UserModel user = UserUtil.getUser(getRequest());
		String code = reqData.getString("agentCode");
		String userId = "";
		String extUserId = reqData.getString("customerUserId");
		String extUserName = reqData.getString("customerUserName");
		// 增加详细的参数日志记录
		logger.info("开始保存坐席和客户信息关系: agentCode=" + code + ", customerUserId=" + extUserId +
				   ", customerUserName=" + extUserName + ", agentAcc=" + (user != null ? user.getUserAcc() : "null"));
		if(StringUtils.isBlank(code)) {
			logger.warn("agentCode参数为空，无法保存埋点记录: customerUserId=" + extUserId +
					   ", agentAcc=" + (user != null ? user.getUserAcc() : "null"));
			return;
		}
		try {
			JSONObject result = new JSONObject();
			JSONObject request = new JSONObject();
			request.put("command", "getuserinfo");
			request.put("entId", "1000");
			request.put("code", code);
			IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
			result = service.invoke(request);
			logger.info("企微接口调用结果: " + (result != null ? result.toString() : "null"));
			JSONObject respData = result.getJSONObject("respData");
			if (StringUtils.isNotBlank(respData.getString("errcode")) && "0".equals(respData.getString("errcode"))) {
				if (StringUtils.isNotBlank(respData.getString("userid"))||StringUtils.isNotBlank(respData.getString("UserId"))) {
					// 成员信息
					userId = respData.getString("userid");
					//取不到再取一次
					userId = StringUtils.isBlank(userId) ? respData.getString("UserId"):userId;
					logger.info("获取到企业成员userId: " + userId);
				} else {
					// 非企业成员时
					userId = respData.getString("external_userid");
					//取不到再取一次
					userId = StringUtils.isBlank(userId) ? respData.getString("EXTERNAL_USERID"):userId;
					logger.info("获取到外部成员userId: " + userId);
				}
			} else {
				logger.warn("企微接口返回错误: errcode=" + respData.getString("errcode") +
						   ", errmsg=" + respData.getString("errmsg"));
				// 即使获取用户信息失败，也尝试插入基础埋点记录
				userId = "UNKNOWN_" + code;
			}
			EasyQuery query = EasyQuery.getDb(Constants.APP_NAME, Constants.DS_YW);
			String currentTime = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			String beginTime = DateUtil.addDay("yyyy-MM-dd", currentTime, -1) + " 18:00:00";
			String endTime = currentTime + " 18:00:00";
			logger.info("检查重复记录: userId=" + userId + ", extUserId=" + extUserId +
					   ", agentAcc=" + user.getUserAcc() + ", timeRange=" + beginTime + " to " + endTime);
			EasySQL sql = new EasySQL("select count(1) from C_NO_WECOM_LOGIN_LOG where 1=1");
			sql.append(userId,"and wx_agent_userid = ?");
			sql.append(extUserId,"and wx_ext_userid = ?");
			sql.append(user.getUserAcc(),"and agent_acc = ?");
			sql.append(beginTime,"and create_time >= ?");
			sql.append(endTime,"and create_time < ?");
			boolean flag = query.queryForExist(sql.getSQL(), sql.getParams());
			if(flag) {//表示存在该记录，当天不用再入库
				logger.info("当天已存在相同的埋点记录，跳过插入: userId=" + userId + ", extUserId=" + extUserId);
				return;
			}
			String recordId = RandomKit.uniqueStr();
			EasyRecord record = new EasyRecord("C_NO_WECOM_LOGIN_LOG","ID");
			record.put("ID", recordId);
			record.put("DATE_ID", DateUtil.getCurrentDateStr("yyyyMMdd"));
			record.put("AGENT_ACC", user.getUserAcc());
			record.put("AGENT_NO", user.getUserNo());
			record.put("AGENT_DEPT", user.getDeptCode());
			record.put("AGENT_NAME", user.getUserName());
			record.put("AGENT_AREA", user.getAreaCode());
			record.put("WX_AGENT_USERID", userId);
			record.put("WX_EXT_USERID", extUserId);
			record.put("WX_EXT_NAME", extUserName);
			record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			logger.info("准备插入埋点记录: ID=" + recordId + ", AGENT_ACC=" + user.getUserAcc() +
					   ", WX_AGENT_USERID=" + userId + ", WX_EXT_USERID=" + extUserId);
			query.save(record);
			logger.info("埋点记录插入成功: ID=" + recordId);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
		}
	}


	public JSONObject actionForCreateMessage() {
		MessageService ms = new MessageService();
		ms.handleSession();
		return EasyResult.ok();
	}

	/**
	 * 跳转主动服务页面
	 * 需通过企微构造网页授权链接	
	 */
	public void actionForAsPage() {
		String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=CORPID&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect";
		url = url.replace("CORPID", Constants.WECOM_CORPID);
		String uri = Constants.MIDEA_WEBSITE + "wecom/servlet/config?action=AsJump";
		try {
			url = url.replace("REDIRECT_URI", URLEncoder.encode(uri,"UTF-8"));
		} catch (UnsupportedEncodingException e) {
			logger.error("ERROR:" + e.getMessage(), e);
		}
		try {
			getResponse().sendRedirect(url);
		} catch (IOException e) {
			logger.error("ERROR:" + e.getMessage(), e);
		}
	}

	public void actionForAsJump() {
		String code = getRequest().getParameter("code");
		if(StringUtils.isBlank(code)) {
			return;
		}
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject();
		String userId = "";
		request.put("command", "getuserinfo");
		request.put("entId", "1000");
		request.put("code", code);
		try {
			IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
			result = service.invoke(request);
		} catch (ServiceException e) {
			logger.error("error" + e.getMessage(), e);
		}
		JSONObject respData = result.getJSONObject("respData");
		if (StringUtils.isNotBlank(respData.getString("errcode")) && respData.getString("errcode").equals("0")) {
			if (StringUtils.isNotBlank(respData.getString("UserId"))) {
				// 成员信息
				userId = respData.getString("UserId");
			} else {
				// 非企业成员时
				userId = respData.getString("EXTERNAL_USERID");
			}
		}
		try {
			logger.info("===========url:" + "/activeService/access/index.jsp?wechatUserId=" + userId);
			getResponse().sendRedirect("/activeService/access/index.jsp?wechatUserId=" + userId);
		} catch (IOException e) {
			logger.error("error:" + e.getMessage(),e);
		}
	}


}
